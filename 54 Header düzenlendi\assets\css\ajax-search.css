/* AJAX Search Styles */
.ajax-search-container {
    position: relative;
    width: 100%;
}

.ajax-search-container .search-form {
    position: relative;
    width: 100%;
    display: flex !important; /* Eski tema stilini koru */
}

/* Search input wrapper */
.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    width: 100%; /* <PERSON> geni<PERSON>lik */
    flex-grow: 1; /* Eski tema stilini koru */
    height: 50px;
}

.search-input-wrapper:focus-within {
    border-color: #ff6000;
    box-shadow: 0 2px 8px rgba(255, 96, 0, 0.2);
}

/* Search field */
.ajax-search-container .search-field {
    flex: 1;
    border: none !important;
    outline: none !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    background: transparent !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    margin: 0 !important;
}

.ajax-search-container .search-field::placeholder {
    color: #999;
}

/* Tarayici varsayilan search clear butonunu gizle */
.ajax-search-container .search-field::-webkit-search-cancel-button {
    -webkit-appearance: none;
    appearance: none;
}

.ajax-search-container .search-field::-webkit-search-decoration {
    -webkit-appearance: none;
    appearance: none;
}

.ajax-search-container .search-field::-ms-clear {
    display: none;
}

/* Search icon button */
.search-icon-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    color: #ff6000;
    cursor: pointer;
    font-size: 18px; /* 16px'den 18px'e buyuttuk */
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px; /* Minimum genislik ekledik */
}

.search-icon-btn:hover {
    color: #e55a00;
}

/* Loading indicator */
.ajax-search-loading {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: none;
    color: #ff6000;
    font-size: 16px;
    z-index: 10;
}

.ajax-search-loading.show {
    display: block;
}



/* Popular searches styles */
.popular-searches-header {
    padding: 15px 20px 10px;
    font-weight: bold;
    color: #333;
    border-bottom: 1px solid #eee;
    background: #f9f9f9;
    font-size: 14px;
}

.popular-search-item {
    display: block;
    padding: 12px 20px;
    text-decoration: none;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.popular-search-item:hover,
.popular-search-item.keyboard-highlighted {
    background-color: #f8f8f8;
    color: #ff6000;
}

.popular-search-item .search-item-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.popular-icon {
    color: #ff6000;
    font-size: 14px;
}

.popular-search-item .search-item-title {
    font-size: 14px;
}

/* Loading states */
.loading-popular,
.no-popular {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.loading-popular {
    color: #ff6000;
}

/* Search results dropdown */
.ajax-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ccc; /* Tema border rengini kullan */
    border-top: none;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
    display: none;
}

.ajax-search-results.show {
    display: block;
}

.ajax-search-results-inner {
    padding: 0;
}

/* Individual search result item */
.ajax-search-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.ajax-search-item:hover,
.ajax-search-item.highlighted {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.ajax-search-item:last-child {
    border-bottom: none;
}

/* Product image */
.ajax-search-item-image {
    width: 50px;
    height: 50px;
    margin-right: 12px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ajax-search-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.ajax-search-item-image .no-image {
    color: #999;
    font-size: 20px;
}

/* Product details */
.ajax-search-item-details {
    flex: 1;
    min-width: 0;
}

.ajax-search-item-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.3;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.ajax-search-item-price {
    font-size: 13px;
    color: #ff6000; /* Tema rengini kullan */
    font-weight: 600;
    margin: 0;
}

.ajax-search-item-price .woocommerce-Price-amount {
    font-weight: 600;
}

/* No results message */
.ajax-search-no-results {
    padding: 20px 16px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* View all results link */
.ajax-search-view-all {
    display: block;
    padding: 12px 16px;
    background: #f8f9fa;
    color: #ff6000; /* Tema rengini kullan */
    text-decoration: none;
    text-align: center;
    font-weight: 600;
    border-top: 1px solid #e0e0e0;
    transition: background-color 0.2s ease;
}

.ajax-search-view-all:hover {
    background: #e9ecef;
    text-decoration: none;
    color: #cc4d00; /* Tema hover rengini kullan */
}

/* Responsive design */
@media (max-width: 768px) {
    .ajax-search-results {
        max-height: 300px;
    }
    
    .ajax-search-item {
        padding: 10px 12px;
    }
    
    .ajax-search-item-image {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
    
    .ajax-search-item-title {
        font-size: 13px;
    }
    
    .ajax-search-item-price {
        font-size: 12px;
    }
}

/* Keyboard navigation highlight */
.ajax-search-item.keyboard-highlighted {
    background-color: #fff3e0; /* Tema rengine uygun highlight */
}

/* Search overlay for mobile */
@media (max-width: 480px) {
    .ajax-search-container.mobile-active {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        background: rgba(0, 0, 0, 0.5);
        padding: 20px;
    }
    
    .ajax-search-container.mobile-active .search-form {
        margin-bottom: 10px;
    }
    
    .ajax-search-container.mobile-active .ajax-search-results {
        position: static;
        border-radius: 8px;
        max-height: calc(100vh - 120px);
    }
}

/* Animation for dropdown */
.ajax-search-results {
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.ajax-search-results.show {
    opacity: 1;
    transform: translateY(0);
}

/* Highlight search terms */
.ajax-search-highlight {
    background-color: #fff3cd;
    font-weight: 600;
    padding: 1px 2px;
    border-radius: 2px;
}
