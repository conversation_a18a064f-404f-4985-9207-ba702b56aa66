/**
 * AJAX Search Functionality for WooCommerce Products
 */
(function($) {
    'use strict';

    let searchTimeout;
    let currentRequest;
    let selectedIndex = -1;

    const searchContainer = $('.ajax-search-container');
    const searchInput = $('#ajax-search-input');
    const searchResultsDropdown = $('#ajax-search-results');
    const searchLoading = $('#ajax-search-loading');
    const searchForm = $('.search-form');

    // Initialize search functionality
    function initAjaxSearch() {
        if (!searchInput.length) {
            return;
        }

        // Bind events
        searchInput.on('input', handleSearchInput);
        searchInput.on('keydown', handleKeyNavigation);
        searchInput.on('focus', handleSearchFocus);
        
        // Close dropdown when clicking outside
        $(document).on('click', function(e) {
            if (!searchContainer.is(e.target) && searchContainer.has(e.target).length === 0) {
                hideSearchResults();
            }
        });

        // Handle ESC key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // ESC key
                hideSearchResults();
                searchInput.blur();
            }
        });

        // Prevent form submission if dropdown is open
        searchForm.on('submit', function(e) {
            if (searchResultsDropdown.hasClass('show') && selectedIndex >= 0) {
                e.preventDefault();
                selectCurrentItem();
            }
        });
    }

    // Handle search input
    function handleSearchInput() {
        const query = searchInput.val().trim();
        
        // Clear previous timeout
        clearTimeout(searchTimeout);
        
        // Cancel previous request
        if (currentRequest) {
            currentRequest.abort();
        }

        if (query.length === 0) {
            hideSearchResults();
            return;
        }

        // Show loading after a short delay
        searchTimeout = setTimeout(function() {
            if (query.length >= 1) {
                performSearch(query);
            }
        }, 300); // 300ms debounce
    }

    // Handle keyboard navigation
    function handleKeyNavigation(e) {
        const items = searchResultsDropdown.find('.ajax-search-item');
        
        switch(e.keyCode) {
            case 40: // Down arrow
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelection(items);
                break;
                
            case 38: // Up arrow
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, -1);
                updateSelection(items);
                break;
                
            case 13: // Enter
                if (selectedIndex >= 0 && items.length > 0) {
                    e.preventDefault();
                    selectCurrentItem();
                }
                break;
        }
    }

    // Handle search focus
    function handleSearchFocus() {
        const query = searchInput.val().trim();
        if (query.length >= 1 && searchResultsDropdown.find('.ajax-search-item').length > 0) {
            showSearchResults();
        }
    }

    // Perform AJAX search
    function performSearch(query) {
        showLoading();

        currentRequest = $.ajax({
            url: dmrthema_ajax_search.ajax_url,
            type: 'POST',
            data: {
                action: 'dmrthema_ajax_search',
                query: query,
                nonce: dmrthema_ajax_search.nonce
            },
            success: function(response) {
                hideLoading();

                if (response.success && response.data) {
                    displaySearchResults(response.data, query);
                } else {
                    displayNoResults();
                }
            },
            error: function(xhr, status, error) {
                hideLoading();
                if (status !== 'abort') {
                    displayNoResults();
                }
            },
            complete: function() {
                currentRequest = null;
            }
        });
    }

    // Display search results
    function displaySearchResults(data, query) {
        let html = '';
        
        if (data.products && data.products.length > 0) {
            data.products.forEach(function(product) {
                html += buildProductItem(product, query);
            });
            
            // Add "View all results" link
            if (data.total > data.products.length) {
                html += '<a href="' + data.search_url + '" class="ajax-search-view-all">';
                html += 'Tum sonuclari gor (' + data.total + ' urun)';
                html += '</a>';
            }
        } else {
            html = '<div class="ajax-search-no-results">Aradiginiz kriterlere uygun urun bulunamadi.</div>';
        }
        
        searchResultsDropdown.find('.ajax-search-results-inner').html(html);
        showSearchResults();
        resetSelection();

        // Bind click events to result items
        searchResultsDropdown.find('.ajax-search-item').on('click', function(e) {
            e.preventDefault();
            window.location.href = $(this).attr('href');
        });
    }

    // Build product item HTML
    function buildProductItem(product, query) {
        const highlightedTitle = highlightSearchTerms(product.title, query);
        const imageHtml = product.image ? 
            '<img src="' + product.image + '" alt="' + product.title + '">' :
            '<i class="fas fa-image no-image"></i>';
            
        return '<a href="' + product.url + '" class="ajax-search-item">' +
               '<div class="ajax-search-item-image">' + imageHtml + '</div>' +
               '<div class="ajax-search-item-details">' +
               '<h4 class="ajax-search-item-title">' + highlightedTitle + '</h4>' +
               '<div class="ajax-search-item-price">' + product.price + '</div>' +
               '</div>' +
               '</a>';
    }

    // Highlight search terms in text
    function highlightSearchTerms(text, query) {
        if (!query) return text;
        
        const regex = new RegExp('(' + query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + ')', 'gi');
        return text.replace(regex, '<span class="ajax-search-highlight">$1</span>');
    }

    // Display no results message
    function displayNoResults() {
        const html = '<div class="ajax-search-no-results">Aradiginiz kriterlere uygun urun bulunamadi.</div>';
        searchResultsDropdown.find('.ajax-search-results-inner').html(html);
        showSearchResults();
        resetSelection();
    }

    // Show search results dropdown
    function showSearchResults() {
        searchResultsDropdown.addClass('show');
        selectedIndex = -1;
    }

    // Hide search results dropdown
    function hideSearchResults() {
        searchResultsDropdown.removeClass('show');
        resetSelection();
    }

    // Show loading indicator
    function showLoading() {
        searchLoading.addClass('show');
    }

    // Hide loading indicator
    function hideLoading() {
        searchLoading.removeClass('show');
    }

    // Update selection highlighting
    function updateSelection(items) {
        items.removeClass('keyboard-highlighted');
        
        if (selectedIndex >= 0 && selectedIndex < items.length) {
            const selectedItem = items.eq(selectedIndex);
            selectedItem.addClass('keyboard-highlighted');
            
            // Scroll to selected item if needed
            const container = searchResultsDropdown;
            const itemTop = selectedItem.position().top;
            const itemBottom = itemTop + selectedItem.outerHeight();
            const containerHeight = container.height();
            const scrollTop = container.scrollTop();

            if (itemBottom > containerHeight) {
                container.scrollTop(scrollTop + itemBottom - containerHeight);
            } else if (itemTop < 0) {
                container.scrollTop(scrollTop + itemTop);
            }
        }
    }

    // Select current highlighted item
    function selectCurrentItem() {
        const items = searchResultsDropdown.find('.ajax-search-item');
        if (selectedIndex >= 0 && selectedIndex < items.length) {
            const selectedItem = items.eq(selectedIndex);
            window.location.href = selectedItem.attr('href');
        }
    }

    // Reset selection
    function resetSelection() {
        selectedIndex = -1;
        searchResultsDropdown.find('.ajax-search-item').removeClass('keyboard-highlighted');
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initAjaxSearch();
    });

})(jQuery);
