/**
 * User Avatar Dropdown JavaScript
 */

// <PERSON>fa yuklendiginde calistir
document.addEventListener('DOMContentLoaded', function() {
    const avatarButton = document.getElementById('user-avatar-toggle');
    const dropdownMenu = document.getElementById('user-dropdown-menu');
    
    if (!avatarButton || !dropdownMenu) {
        return; // Elementler bulunamazsa cik
    }
    
    // Avatar butonuna tiklandiginda dropdown'u ac/kapat
    avatarButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const isActive = dropdownMenu.classList.contains('active');
        
        if (isActive) {
            closeDropdown();
        } else {
            openDropdown();
        }
    });
    
    // Dropdown disina tiklandiginda kapat
    document.addEventListener('click', function(e) {
        if (!avatarButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
            closeDropdown();
        }
    });
    
    // ESC tusuna basildiginda kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDropdown();
        }
    });
    
    // Dropdown'u ac
    function openDropdown() {
        dropdownMenu.classList.add('active');
        avatarButton.setAttribute('aria-expanded', 'true');
    }
    
    // Dropdown'u kapat
    function closeDropdown() {
        dropdownMenu.classList.remove('active');
        avatarButton.setAttribute('aria-expanded', 'false');
    }
});
